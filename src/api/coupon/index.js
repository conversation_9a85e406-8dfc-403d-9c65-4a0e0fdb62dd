import request from "@/utils/request";

// 查询优惠券列表
export function couponList(data) {
  return request({
    url: "/ticketManage/list",
    method: "post",
    data: data,
  });
}

// 新增优惠券
export function addCoupon(data) {
  return request({
    url: "/ticketManage/generateTicketAndCoupon",
    method: "post",
    data: data,
  });
}

// 修改优惠券
export function updateCoupon(data) {
  return request({
    url: "/ticketManage/edit",
    method: "post",
    data: data,
  });
}

// 删除优惠券
export function delCoupon(data) {
  return request({
    url: "/ticketManage/delete",
    method: "post",
    data: data,
  });
}

// 获取单个优惠券详情
export function getCoupon(data) {
  return request({
    url: "/ticketManage/getInfo",
    method: "post",
    data: data,
  });
}

// 批量作废优惠券
export function batchInvalidCoupon(data) {
  return request({
    url: "/ticketManage/invalid",
    method: "post",
    data: data,
  });
}

// 批量绑定优惠券
export function batchBindCoupon(data) {
  return request({
    url: "/ticketManage/bind",
    method: "post",
    data: data,
  });
}
