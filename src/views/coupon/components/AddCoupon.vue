<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="60%"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="small"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优惠券类型" prop="couponType">
            <el-select
              v-model="form.couponType"
              placeholder="请选择优惠券类型"
              style="width: 100%"
              @change="handleCouponTypeChange"
            >
              <el-option label="无门槛券" value="0" />
              <el-option label="满减券" value="1" />
              <el-option label="免票券" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品类型" prop="ticketType">
            <el-select
              v-model="form.ticketType"
              placeholder="请选择产品类型"
              style="width: 100%"
            >
              <el-option label="低空游览" value="0" />
              <el-option label="交通旅游" value="1" />
              <el-option label="通用" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优惠券名称" prop="couponName">
            <el-input
              v-model="form.couponName"
              placeholder="请输入优惠券名称"
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="count">
            <el-input-number
              v-model="form.count"
              :min="1"
              :max="9999"
              controls-position="right"
              style="width: 100%"
              placeholder="请输入数量"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row
        :gutter="20"
        v-if="form.couponType !== '2' && form.couponType !== '3'"
      >
        <el-col :span="12">
          <el-form-item label="减免价格" prop="discountAmount">
            <el-input-number
              v-model="form.discountAmount"
              :min="0"
              :precision="2"
              controls-position="right"
              style="width: 100%"
              placeholder="请输入减免价格"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单价格要求" prop="minAmount">
            <el-input-number
              v-model="form.minAmount"
              :min="0"
              :precision="2"
              controls-position="right"
              style="width: 100%"
              placeholder="请输入订单价格要求"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="form.couponType === '2'">
        <el-col :span="12">
          <el-form-item label="减免价格" prop="discountAmount">
            <el-input-number
              v-model="form.discountAmount"
              :min="0"
              :precision="2"
              controls-position="right"
              style="width: 100%"
              placeholder="请输入减免价格"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否可转赠" prop="isTransferable">
            <el-radio-group v-model="form.isTransferable">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="有效期" prop="couponTime">
            <el-date-picker
              v-model="form.couponTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="绑定用户" prop="bindPhone">
            <el-input
              v-model="form.bindPhone"
              placeholder="请输入用户手机号（非必填）"
              maxlength="11"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="使用规则" prop="useRules">
            <Editor v-model="form.useRules" :min-height="200" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "AddCoupon",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "新增优惠券",
    },
    couponData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      form: {
        couponType: "",
        ticketType: "",
        couponName: "",
        discountAmount: null,
        minAmount: null,
        count: 1,
        isTransferable: 1,
        couponTime: [],
        useRules: "",
        bindPhone: "",
      },
      rules: {
        couponType: [
          { required: true, message: "请选择优惠券类型", trigger: "change" },
        ],
        ticketType: [
          { required: true, message: "请选择产品类型", trigger: "change" },
        ],
        couponName: [
          { required: true, message: "请输入优惠券名称", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
        count: [
          { required: true, message: "请输入数量", trigger: "blur" },
          { type: "number", min: 1, message: "数量必须大于0", trigger: "blur" },
        ],
        discountAmount: [
          { required: true, message: "请输入减免价格", trigger: "blur" },
          {
            type: "number",
            min: 0,
            message: "减免价格不能小于0",
            trigger: "blur",
          },
        ],
        minAmount: [
          { required: true, message: "请输入订单价格要求", trigger: "blur" },
          {
            type: "number",
            min: 0,
            message: "订单价格要求不能小于0",
            trigger: "blur",
          },
        ],
        couponStartTime: [
          { required: true, message: "请选择有效期", trigger: "change" },
        ],
        isTransferable: [
          { required: true, message: "请选择是否可转赠", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
      }
    },
    couponData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = { ...this.form, ...val };
          if (val.startTime && val.endTime) {
            this.form.couponStartTime = [val.startTime, val.endTime];
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initForm() {
      if (Object.keys(this.couponData).length === 0) {
        this.resetForm();
      }
      this.updateRules();
    },
    resetForm() {
      this.form = {
        couponType: "",
        ticketType: "",
        couponName: "",
        discountAmount: null,
        minAmount: null,
        count: 1,
        isTransferable: 1,
        couponStartTime: [],
        useRules: "",
        bindPhone: "",
      };
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    handleCouponTypeChange(value) {
      // 无门槛券：没有订单价格要求，不可转赠
      if (value === "2") {
        this.form.minAmount = null;
        this.form.isTransferable = 0;
      }
      // 免票券：没有金额限制
      if (value === "3") {
        this.form.discountAmount = null;
        this.form.minAmount = null;
      }
      this.updateRules();
    },
    updateRules() {
      const newRules = { ...this.rules };

      // 根据优惠券类型动态调整验证规则
      if (this.form.couponType === "2") {
        // 无门槛券：需要减免价格，不需要订单价格要求
        newRules.discountAmount = [
          { required: true, message: "请输入减免价格", trigger: "blur" },
          {
            type: "number",
            min: 0,
            message: "减免价格不能小于0",
            trigger: "blur",
          },
        ];
        delete newRules.minAmount;
      } else if (this.form.couponType === "3") {
        // 免票券：不需要金额限制
        delete newRules.discountAmount;
        delete newRules.minAmount;
      } else if (this.form.couponType === "1") {
        // 满减券：需要减免价格和订单价格要求
        newRules.discountAmount = [
          { required: true, message: "请输入减免价格", trigger: "blur" },
          {
            type: "number",
            min: 0,
            message: "减免价格不能小于0",
            trigger: "blur",
          },
        ];
        newRules.minAmount = [
          { required: true, message: "请输入订单价格要求", trigger: "blur" },
          {
            type: "number",
            min: 0,
            message: "订单价格要求不能小于0",
            trigger: "blur",
          },
        ];
      }

      this.rules = newRules;
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          const formData = { ...this.form };

          // 处理有效期
          if (formData.couponTime && formData.couponTime.length === 2) {
            formData.couponStartTime = formData.couponTime[0];
            formData.couponEndTime = formData.couponTime[1];
            delete formData.couponTime;
          }

          this.$emit("submit", formData);
        }
      });
    },
    handleClose() {
      this.loading = false;
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    setLoading(loading) {
      this.loading = loading;
    },
  },
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
